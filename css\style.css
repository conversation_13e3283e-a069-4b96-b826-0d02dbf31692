/*
 * Copyright (c) 2025 Eria Software solutions and services private limited.
 * See LICENSE.md for license details.
 */

/* <PERSON><PERSON> - Custom CSS */

/* Root Variables for Consistent Theming */
:root {
    --primary-color: #2E7D32;
    --primary-light: #4CAF50;
    --primary-dark: #1B5E20;
    --secondary-color: #FF8F00;
    --secondary-light: #FFB74D;
    --accent-color: #D32F2F;
    --success-color: #388E3C;
    --warning-color: #F57C00;
    --info-color: #1976D2;
    --light-bg: #F8F9FA;
    --dark-text: #212529;
    --muted-text: #6C757D;
    --border-color: #DEE2E6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.5rem;
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
    background-color: #fff;
}

/* Custom Bootstrap Color Classes */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: var(--transition);
}
.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
}

.text-orange { color: var(--secondary-color) !important; }
.text-brown { color: #8D6E63 !important; }

/* Navigation Styles */
.navbar {
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 600;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    padding-top: 100px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-image img {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.hero-image img:hover {
    transform: scale(1.05);
}

/* Page Header */
.page-header {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, var(--light-bg) 0%, #e9ecef 100%);
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Category Cards */
.category-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

/* Product Cards */
.product-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-info {
    padding: 1rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-text);
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.product-price .original-price {
    font-size: 0.9rem;
    color: var(--muted-text);
    text-decoration: line-through;
    margin-left: 0.5rem;
}

.product-description {
    color: var(--muted-text);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Contact Cards */
.contact-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* Cart Styles */
.cart-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Button Animations */
.btn {
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Loading Spinner */
.spinner-border-primary {
    color: var(--primary-color);
}

/* Success Icon Animation */
.success-icon {
    animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 80px;
        text-align: center;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .category-card {
        margin-bottom: 1rem;
    }
    
    .product-card {
        margin-bottom: 1.5rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 100px 0 50px;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .feature-card,
    .category-card,
    .contact-card {
        margin-bottom: 1rem;
    }
}

/* Utility Classes */
.text-decoration-none {
    text-decoration: none !important;
}

.cursor-pointer {
    cursor: pointer;
}

.border-radius {
    border-radius: var(--border-radius);
}

.shadow-custom {
    box-shadow: var(--shadow);
}

.shadow-lg-custom {
    box-shadow: var(--shadow-lg);
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }
    
    .container {
        max-width: 100% !important;
    }
}
