/*
 * Copyright (c) 2025 Eria Software solutions and services private limited.
 * See LICENSE.md for license details.
 */

/* Modern Electricals - Custom CSS */

/* Root Variables for Consistent Theming - Saffron Color Scheme */
:root {
    --primary-color: #FF8C00;        /* Deep Saffron */
    --primary-light: #FFA500;        /* Light Saffron */
    --primary-dark: #E67E00;         /* Dark Saffron */
    --secondary-color: #FF6B35;      /* Orange Red */
    --secondary-light: #FF8A65;      /* Light Orange */
    --accent-color: #FFD700;         /* Golden Yellow */
    --success-color: #4CAF50;        /* Green for success states */
    --warning-color: #FF9800;        /* Amber for warnings */
    --info-color: #2196F3;          /* Blue for info */
    --light-bg: #FFF8F0;            /* Very light saffron background */
    --dark-text: #2C1810;           /* Dark brown text */
    --muted-text: #8D6E63;          /* <PERSON> muted text */
    --border-color: #FFE0B2;        /* Light saffron border */
    --shadow: 0 0.125rem 0.25rem rgba(255, 140, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(255, 140, 0, 0.25);
    --border-radius: 0.75rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #FF8C00 0%, #FF6B35 100%);
    --gradient-light: linear-gradient(135deg, #FFF8F0 0%, #FFE0B2 100%);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
    background-color: #fff;
}

/* Custom Bootstrap Color Classes */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: var(--transition);
}
.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
}

.text-orange { color: var(--secondary-color) !important; }
.text-brown { color: #8D6E63 !important; }

/* Navigation Styles */
.navbar {
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 600;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    padding-top: 100px;
    background: var(--gradient-light);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23FF8C00" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-image img {
    border-radius: var(--border-radius);
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
}

.hero-image img:hover {
    transform: scale(1.05) rotate(1deg);
}

/* Page Header */
.page-header {
    padding: 120px 0 60px;
    background: var(--gradient-light);
    position: relative;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.1;
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Category Cards */
.category-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

/* Product Cards */
.product-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-info {
    padding: 1rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-text);
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.product-price .original-price {
    font-size: 0.9rem;
    color: var(--muted-text);
    text-decoration: line-through;
    margin-left: 0.5rem;
}

.product-description {
    color: var(--muted-text);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Contact Cards */
.contact-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* Cart Styles */
.cart-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Button Animations */
.btn {
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Loading Spinner */
.spinner-border-primary {
    color: var(--primary-color);
}

/* Success Icon Animation */
.success-icon {
    animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Electrical-specific Animations */
@keyframes electricPulse {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-color);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-light);
    }
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    25% {
        transform: scale(1.1) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 0.6;
    }
    75% {
        transform: scale(1.1) rotate(270deg);
        opacity: 0.8;
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Animation Classes */
.animate-on-scroll {
    opacity: 0;
    transition: var(--transition);
}

.animate-on-scroll.animate {
    opacity: 1;
    animation: fadeInUp 0.8s ease-out;
}

.electric-glow:hover {
    animation: electricPulse 2s infinite;
}

.sparkle-icon {
    animation: sparkle 3s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 80px;
        text-align: center;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .category-card {
        margin-bottom: 1rem;
    }
    
    .product-card {
        margin-bottom: 1.5rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 100px 0 50px;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .feature-card,
    .category-card,
    .contact-card {
        margin-bottom: 1rem;
    }

    /* Mobile-specific improvements */
    .page-header {
        padding: 120px 0 60px;
    }

    .page-header .display-4 {
        font-size: 1.8rem;
    }

    .service-detail {
        padding: 2rem 1rem;
    }

    .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }

    .d-flex.gap-3 .btn {
        width: 100%;
    }

    /* Product grid mobile optimization */
    .product-card {
        margin-bottom: 1.5rem;
    }

    .product-card .card-body {
        padding: 1rem;
    }

    /* Navigation improvements */
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        text-align: center;
    }

    /* Footer mobile optimization */
    footer .col-md-4,
    footer .col-md-2 {
        text-align: center;
        margin-bottom: 2rem;
    }

    /* Stats section mobile */
    .stats-section .display-4 {
        font-size: 2.5rem;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* Utility Classes */
.text-decoration-none {
    text-decoration: none !important;
}

.cursor-pointer {
    cursor: pointer;
}

.border-radius {
    border-radius: var(--border-radius);
}

.shadow-custom {
    box-shadow: var(--shadow);
}

.shadow-lg-custom {
    box-shadow: var(--shadow-lg);
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }
    
    .container {
        max-width: 100% !important;
    }
}
