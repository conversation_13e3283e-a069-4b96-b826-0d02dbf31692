/*
 * Copyright (c) 2025 Eria Software solutions and services private limited.
 * See LICENSE.md for license details.
 */

// Modern Electricals - Animation Scripts

document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    initScrollAnimations();
    initElectricalEffects();
    initCounterAnimations();
    initParallaxEffects();
});

// Scroll-triggered animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Electrical-themed effects
function initElectricalEffects() {
    // Add electric glow effect on hover for buttons
    document.querySelectorAll('.electric-glow').forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 0 20px rgba(255, 140, 0, 0.6), 0 0 40px rgba(255, 140, 0, 0.4)';
        });

        element.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
    });

    // Sparkle effect for electrical icons
    document.querySelectorAll('.sparkle-icon').forEach(icon => {
        setInterval(() => {
            icon.style.textShadow = '0 0 10px #FFD700, 0 0 20px #FF8C00, 0 0 30px #FF6B35';
            setTimeout(() => {
                icon.style.textShadow = '';
            }, 500);
        }, 3000);
    });
}

// Counter animations for statistics
function initCounterAnimations() {
    const counters = document.querySelectorAll('.hero-section h3');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent.replace(/\D/g, ''));
        const increment = target / 50;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = counter.textContent.replace(/\d+/, target);
                clearInterval(timer);
            } else {
                counter.textContent = counter.textContent.replace(/\d+/, Math.floor(current));
            }
        }, 40);
    };

    // Trigger counter animation when hero section is visible
    const heroObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                counters.forEach(counter => {
                    setTimeout(() => animateCounter(counter), 500);
                });
                heroObserver.unobserve(entry.target);
            }
        });
    });

    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        heroObserver.observe(heroSection);
    }
}

// Parallax effects
function initParallaxEffects() {
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.hero-image img');
        
        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Loading animation
window.addEventListener('load', function() {
    document.body.classList.add('loaded');
    
    // Stagger animation for service cards
    const serviceCards = document.querySelectorAll('.category-card');
    serviceCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

// Navbar scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});

// Add CSS for navbar scroll effect
const style = document.createElement('style');
style.textContent = `
    .navbar.scrolled {
        background-color: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(255, 140, 0, 0.1);
    }
    
    .category-card {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    body.loaded .animate-on-scroll {
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }
`;
document.head.appendChild(style);

// Interactive electrical circuit animation
function createCircuitAnimation() {
    const circuits = document.querySelectorAll('.feature-card');
    
    circuits.forEach((circuit, index) => {
        circuit.addEventListener('mouseenter', function() {
            // Create electric current effect
            const current = document.createElement('div');
            current.className = 'electric-current';
            current.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border: 2px solid transparent;
                border-radius: inherit;
                background: linear-gradient(45deg, transparent, #FF8C00, transparent);
                background-size: 200% 200%;
                animation: electricFlow 2s linear infinite;
                pointer-events: none;
                z-index: 1;
            `;
            
            this.style.position = 'relative';
            this.appendChild(current);
            
            setTimeout(() => {
                if (current.parentNode) {
                    current.parentNode.removeChild(current);
                }
            }, 2000);
        });
    });
}

// Add electric flow animation
const electricStyle = document.createElement('style');
electricStyle.textContent = `
    @keyframes electricFlow {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
`;
document.head.appendChild(electricStyle);

// Initialize circuit animation
setTimeout(createCircuitAnimation, 1000);
